import { ref, reactive, computed } from 'vue';

// 页面引用
export const pageContainer = ref(null);
export const paging = ref(null);

// 日志数据
export const logData = ref([]);

// 日志统计信息
export const logStats = ref({
	total: 0,
	error: 0,
	warn: 0,
	info: 0
});

// 日志级别配置
export const logLevels = ref([
	{ label: '全部', value: 'all', color: '#666' },
	{ label: '错误', value: 'error', color: '#dc2626' },
	{ label: '警告', value: 'warn', color: '#f59e0b' },
	{ label: '信息', value: 'info', color: '#3b82f6' }
]);

// 当前选择的日志级别
export const selectedLevel = ref('all');

// 初始化错误日志数据
export const initErrorLogData = async () => {
	try {
		await loadLogStats();
	} catch (error) {
		console.error('初始化错误日志数据失败:', error);
	}
};

// 加载日志统计信息
const loadLogStats = async () => {
	try {
		// 模拟API调用获取日志统计
		const stats = await mockGetLogStats();
		logStats.value = stats;
	} catch (error) {
		console.error('加载日志统计失败:', error);
	}
};

// 模拟API - 获取日志统计
const mockGetLogStats = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			const errorCount = Math.floor(Math.random() * 50) + 10;
			const warnCount = Math.floor(Math.random() * 30) + 5;
			const infoCount = Math.floor(Math.random() * 100) + 20;
			
			resolve({
				total: errorCount + warnCount + infoCount,
				error: errorCount,
				warn: warnCount,
				info: infoCount
			});
		}, 300);
	});
};

// 分页查询日志
export const queryLogs = async (page, pageSize) => {
	try {
		const res = await loadLogData(page, pageSize);
		paging.value.complete(res);
		paging.value.updateVirtualListRender();
	} catch (error) {
		paging.value.complete([]);
	}
};

// 加载日志数据
const loadLogData = async (page = 1, pageSize = 20) => {
	try {
		// 模拟API调用
		const logs = await mockGetErrorLogs(page, pageSize);
		return logs;
	} catch (error) {
		console.error('加载日志数据失败:', error);
		return [];
	}
};

// 模拟API - 获取错误日志
const mockGetErrorLogs = async (page, pageSize) => {
	return new Promise((resolve) => {
		setTimeout(() => {
			const logs = [];
			const levels = ['error', 'warn', 'info'];
			const messages = [
				'nginx: [error] 24#24: *1 connect() failed (111: Connection refused) while connecting to upstream',
				'nginx: [warn] 24#24: *1 upstream server temporarily disabled while reading response header from upstream',
				'nginx: [error] 24#24: *1 FastCGI sent in stderr: "PHP message: PHP Fatal error: Uncaught Error"',
				'nginx: [warn] 24#24: *1 an upstream response is buffered to a temporary file',
				'nginx: [error] 24#24: *1 open() "/var/www/html/favicon.ico" failed (2: No such file or directory)',
				'nginx: [info] 24#24: *1 client closed connection while waiting for request',
				'nginx: [error] 24#24: *1 recv() failed (104: Connection reset by peer) while reading response header from upstream',
				'nginx: [warn] 24#24: *1 upstream response is buffered to a temporary file'
			];
			
			const files = [
				'/var/log/nginx/error.log',
				'/etc/nginx/sites-available/default',
				'/var/log/nginx/access.log'
			];

			for (let i = 0; i < pageSize; i++) {
				const level = levels[Math.floor(Math.random() * levels.length)];
				const message = messages[Math.floor(Math.random() * messages.length)];
				const file = files[Math.floor(Math.random() * files.length)];
				
				// 过滤日志级别
				if (selectedLevel.value !== 'all' && level !== selectedLevel.value) {
					continue;
				}

				const log = {
					id: `log_${page}_${i}`,
					level: level,
					time: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(), // 最近7天内的随机时间
					message: message,
					file: file,
					details: level === 'error' ? generateErrorDetails() : null,
					expanded: false
				};
				
				logs.push(log);
			}
			
			resolve(logs);
		}, 500);
	});
};

// 生成错误详情
const generateErrorDetails = () => {
	const details = [
		'Stack trace:',
		'#0 /var/www/html/index.php(10): function_name()',
		'#1 /var/www/html/config.php(25): include_once()',
		'#2 {main}',
		'',
		'Request: GET /api/data HTTP/1.1',
		'Host: example.com',
		'User-Agent: Mozilla/5.0 (compatible; bot)',
		'',
		'Response: 500 Internal Server Error'
	];
	
	return details.join('\n');
};

// 虚拟列表变化回调
export const virtualListChange = (list) => {
	logData.value = list;
};

// 刷新回调
export const reload = () => {
	// 重新加载统计信息
	loadLogStats();
};

// 刷新日志
export const refreshLogs = async () => {
	try {
		pageContainer.value?.notify?.info('正在刷新日志...');
		await loadLogStats();
		paging.value?.reload();
		pageContainer.value?.notify?.success('日志刷新成功');
	} catch (error) {
		console.error('刷新日志失败:', error);
		pageContainer.value?.notify?.error('刷新失败，请重试');
	}
};

// 清空日志
export const clearLogs = async () => {
	try {
		uni.showModal({
			title: '确认清空',
			content: '确定要清空所有错误日志吗？此操作不可恢复。',
			success: async (res) => {
				if (res.confirm) {
					pageContainer.value?.notify?.info('正在清空日志...');
					// 模拟API调用
					await new Promise(resolve => setTimeout(resolve, 1000));
					
					// 重置数据
					logStats.value = { total: 0, error: 0, warn: 0, info: 0 };
					paging.value?.reload();
					pageContainer.value?.notify?.success('日志清空成功');
				}
			}
		});
	} catch (error) {
		console.error('清空日志失败:', error);
		pageContainer.value?.notify?.error('清空失败，请重试');
	}
};

// 下载日志
export const downloadLogs = async () => {
	try {
		pageContainer.value?.notify?.info('正在准备下载...');
		
		// 模拟生成日志文件
		await new Promise(resolve => setTimeout(resolve, 1000));
		
		// 在实际项目中，这里应该调用下载API
		pageContainer.value?.notify?.success('日志文件已生成，请查看下载目录');
		
		// 可以使用 uni.downloadFile 来下载文件
		// const downloadUrl = 'https://your-server.com/api/nginx/error-log/download';
		// uni.downloadFile({
		//     url: downloadUrl,
		//     success: (res) => {
		//         if (res.statusCode === 200) {
		//             pageContainer.value?.notify?.success('日志下载成功');
		//         }
		//     },
		//     fail: (err) => {
		//         pageContainer.value?.notify?.error('下载失败');
		//     }
		// });
		
	} catch (error) {
		console.error('下载日志失败:', error);
		pageContainer.value?.notify?.error('下载失败，请重试');
	}
};

// 选择日志级别
export const selectLevel = (level) => {
	selectedLevel.value = level;
	paging.value?.reload();
};

// 格式化时间
export const formatTime = (timeStr) => {
	try {
		const date = new Date(timeStr);
		const now = new Date();
		const diff = now - date;
		
		// 小于1分钟
		if (diff < 60000) {
			return '刚刚';
		}
		
		// 小于1小时
		if (diff < 3600000) {
			const minutes = Math.floor(diff / 60000);
			return `${minutes}分钟前`;
		}
		
		// 小于1天
		if (diff < 86400000) {
			const hours = Math.floor(diff / 3600000);
			return `${hours}小时前`;
		}
		
		// 超过1天，显示具体日期
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		const hour = String(date.getHours()).padStart(2, '0');
		const minute = String(date.getMinutes()).padStart(2, '0');
		
		return `${month}-${day} ${hour}:${minute}`;
	} catch (error) {
		return timeStr;
	}
};

// 获取日志级别样式类
export const getLogLevelClass = (level) => {
	return level;
};

// 切换展开状态
export const toggleExpand = (item) => {
	item.expanded = !item.expanded;
};
